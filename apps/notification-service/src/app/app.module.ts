import { DatabaseModule, QueueModule } from '@libs';
import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { AppController } from './app.controller';
import { HealthController } from './controllers/health.controller';
import { NotificationsController } from './controllers/notifications.controller';
import { AppService } from './app.service';
import { NotificationService } from './services/notification.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.development', '.env'],
    }),
    QueueModule,
    // ScheduleModule.forRoot(),
    DatabaseModule,
    // NotificationModule,
  ],
  controllers: [AppController, NotificationsController, HealthController],
  providers: [AppService, NotificationService],
})
export class AppModule {}
