import { Body, Controller, Logger, Post } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { NotificationService } from '../services/notification.service';
import type { RegisterDeviceDto } from '../dto/register-device.dto';

@ApiTags('Notifications')
@Controller('notifications')
export class NotificationsController {
  private readonly logger = new Logger(NotificationsController.name);
  constructor(private readonly notificationService: NotificationService) {}

  @Post('register-device')
  async registerDevice(@Body() registerDeviceDto: RegisterDeviceDto) {
    this.logger.log('Registering device');
    const device = await this.notificationService.registerDevice(registerDeviceDto);
    return device;
  }
}
