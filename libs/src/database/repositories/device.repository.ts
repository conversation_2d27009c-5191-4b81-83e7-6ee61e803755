import { Injectable } from '@nestjs/common';
import type { RegisterDeviceDto } from '../../shared/dto/register-device.dto';
import { PrismaService } from '../prisma.service';

@Injectable()
export class DeviceRepository {
  /**
   *
   */
  constructor(private readonly prisma: PrismaService) {}

  async registerDevice(device: RegisterDeviceDto) {
    return this.prisma.device.create({
      data: device,
    });
  }
}
