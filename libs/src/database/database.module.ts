import { Global, Module } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { NotificationResultRepository } from './repositories/notification-result.repository';
import { NotificationTemplateRepository } from './repositories/notification-template.repository';
import { NotificationRepository } from './repositories/notification.repository';
import { DeviceRepository } from './repositories/device.repository';

// @Global()
@Module({
  providers: [
    PrismaService,
    DeviceRepository,
    NotificationRepository,
    NotificationResultRepository,
    NotificationTemplateRepository,
  ],
  exports: [
    PrismaService,
    DeviceRepository,
    NotificationRepository,
    NotificationResultRepository,
    NotificationTemplateRepository,
  ],
})
export class DatabaseModule {}
